"use client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import {
  ArrowRight,
  Star,
  TrendingUp,
  Users,
  Shield,
  Zap,
  Sparkles,
  ChevronRight,
  Clock,
  CheckCircle,
  Heart,
  Bookmark,
  Share2
} from "lucide-react"
import { cn } from "@/lib/utils"
import { motion, AnimatePresence } from "framer-motion"
import { useState, useMemo, useCallback } from "react"
import { useTranslations } from '@/hooks/useTranslations'
import {
  Brain,
  Cpu,
  GraduationCap,
  Database,
  Eye,
  Target,
  CheckCircle2,
  Globe,
  BarChart,
  BookOpen,
  Award,
  Settings,
  type LucideIcon
} from "lucide-react"




interface ProductFeature {
  text: string
  iconName: string
}

interface Product {
  slug: string
  name: string
  description: string
  iconName: string
  features: ProductFeature[]
  highlight?: string
  price?: string
  category?: string
}

interface ProductCardProps {
  product: Product
  index: number
}

// 图标映射
const ICON_MAP: Record<string, LucideIcon> = {
  Brain: Brain,
  Cpu: Cpu,
  GraduationCap: GraduationCap,
  Database: Database,
  Eye: Eye,
  Target: Target,
  CheckCircle2: CheckCircle2,
  Globe: Globe,
  BarChart: Bar<PERSON>hart,
  BookOpen: BookOpen,
  Award: Award,
  Settings: Settings,
  Zap: Zap,
  Shield: Shield,
  Users: Users
}

// 渲染图标函数
const renderIcon = (iconName: string, className?: string) => {
  const IconComponent = ICON_MAP[iconName]
  if (!IconComponent) {
    return <div className={cn("bg-blue-500/20 rounded", className)} />
  }
  return <IconComponent className={className} />
}

export function ProductCard({ product, index }: ProductCardProps) {
  const t = useTranslations('productsPage')
  const [isHovered, setIsHovered] = useState(false)
  const [isBookmarked, setIsBookmarked] = useState(false)
  const [isLiked, setIsLiked] = useState(false)
  const [isBookmarkLoading, setIsBookmarkLoading] = useState(false)
  const [isLikeLoading, setIsLikeLoading] = useState(false)

  // 现代化主题色彩系统
  const getThemeColors = (category: string) => {
    switch (category) {
      case 'AI服务':
        return {
          primary: 'from-blue-500 via-blue-600 to-indigo-600',
          secondary: 'from-blue-50/80 via-indigo-50/60 to-purple-50/40',
          accent: '#3B82F6',
          accentRgb: '59, 130, 246',
          light: 'rgba(59, 130, 246, 0.08)',
          dark: 'rgba(59, 130, 246, 0.9)',
          glow: '0 0 40px rgba(59, 130, 246, 0.3)'
        }
      case '云计算':
        return {
          primary: 'from-emerald-500 via-green-600 to-teal-600',
          secondary: 'from-emerald-50/80 via-green-50/60 to-teal-50/40',
          accent: '#10B981',
          accentRgb: '16, 185, 129',
          light: 'rgba(16, 185, 129, 0.08)',
          dark: 'rgba(16, 185, 129, 0.9)',
          glow: '0 0 40px rgba(16, 185, 129, 0.3)'
        }
      case '教育科技':
        return {
          primary: 'from-purple-500 via-violet-600 to-fuchsia-600',
          secondary: 'from-purple-50/80 via-violet-50/60 to-fuchsia-50/40',
          accent: '#8B5CF6',
          accentRgb: '139, 92, 246',
          light: 'rgba(139, 92, 246, 0.08)',
          dark: 'rgba(139, 92, 246, 0.9)',
          glow: '0 0 40px rgba(139, 92, 246, 0.3)'
        }
      default:
        return {
          primary: 'from-slate-500 via-gray-600 to-zinc-600',
          secondary: 'from-slate-50/80 via-gray-50/60 to-zinc-50/40',
          accent: '#64748B',
          accentRgb: '100, 116, 139',
          light: 'rgba(100, 116, 139, 0.08)',
          dark: 'rgba(100, 116, 139, 0.9)',
          glow: '0 0 40px rgba(100, 116, 139, 0.3)'
        }
    }
  }

  const theme = useMemo(() => getThemeColors(product.category || ''), [product.category])

  const handleBookmarkClick = useCallback(async (e: React.MouseEvent) => {
    e.preventDefault()
    if (isBookmarkLoading) return
    
    setIsBookmarkLoading(true)
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 300))
      setIsBookmarked(!isBookmarked)
    } catch (error) {
      console.error('Failed to update bookmark:', error)
    } finally {
      setIsBookmarkLoading(false)
    }
  }, [isBookmarked, isBookmarkLoading])

  const handleLikeClick = useCallback(async (e: React.MouseEvent) => {
    e.preventDefault()
    if (isLikeLoading) return
    
    setIsLikeLoading(true)
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 300))
      setIsLiked(!isLiked)
    } catch (error) {
      console.error('Failed to update like:', error)
    } finally {
      setIsLikeLoading(false)
    }
  }, [isLiked, isLikeLoading])

  const handleHoverStart = useCallback(() => setIsHovered(true), [])
  const handleHoverEnd = useCallback(() => setIsHovered(false), [])

  return (
    <motion.div
      className="group relative h-full"
      initial={{ opacity: 0, y: 30, scale: 0.95 }}
      whileInView={{ opacity: 1, y: 0, scale: 1 }}
      transition={{
        duration: 0.6,
        delay: index * 0.15,
        type: "spring",
        stiffness: 100
      }}
      viewport={{ once: true }}
      whileHover={{
        y: -12,
        transition: { duration: 0.3, type: "spring", stiffness: 300 }
      }}
      onHoverStart={handleHoverStart}
      onHoverEnd={handleHoverEnd}
      role="article"
      aria-label={`${product.name} - ${product.description}`}
    >
      {/* 主卡片容器 */}
      <div className="relative h-full">
        {/* 背景光晕效果 */}
        <motion.div
          className="absolute -inset-1 rounded-[2rem] opacity-0 group-hover:opacity-100 transition-opacity duration-500"
          style={{
            background: `linear-gradient(135deg, ${theme.primary})`,
            filter: 'blur(20px)',
          }}
          animate={{
            scale: isHovered ? 1.02 : 1,
          }}
        />

        {/* 主卡片 */}
        <div className={cn(
          "relative h-full rounded-[1.75rem] bg-white/95 backdrop-blur-xl",
          "border border-white/20 shadow-xl overflow-hidden",
          "transition-all duration-500 group-hover:shadow-2xl",
          "group-hover:bg-white/98"
        )}>

          {/* 顶部渐变条 */}
          <div
            className="absolute top-0 left-0 right-0 h-1 opacity-80"
            style={{ background: `linear-gradient(90deg, ${theme.primary})` }}
          />

          {/* 背景装饰网格 */}
          <div className="absolute inset-0 opacity-[0.02]">
            <div className="absolute inset-0" style={{
              backgroundImage: `radial-gradient(circle at 1px 1px, ${theme.accent} 1px, transparent 0)`,
              backgroundSize: '24px 24px'
            }} />
          </div>

          {/* 浮动装饰元素 */}
          <div className="absolute top-6 right-6 w-20 h-20 opacity-5 group-hover:opacity-10 transition-opacity duration-500">
            <motion.div
              className="w-full h-full rounded-full"
              style={{ background: `linear-gradient(135deg, ${theme.primary})` }}
              animate={{
                rotate: 360,
                scale: [1, 1.1, 1],
              }}
              transition={{
                rotate: { duration: 20, repeat: Infinity, ease: "linear" },
                scale: { duration: 4, repeat: Infinity, ease: "easeInOut" }
              }}
            />
          </div>

          {/* 高亮标签 */}
          {product.highlight && (
            <motion.div
              className="absolute -top-3 -right-3 z-20"
              initial={{ scale: 0, rotate: -10 }}
              animate={{ scale: 1, rotate: 0 }}
              transition={{ delay: 0.3, type: "spring", stiffness: 200 }}
            >
              <div className={cn(
                "px-4 py-2 rounded-full text-xs font-bold text-white shadow-lg",
                "relative overflow-hidden"
              )}
              style={{ background: `linear-gradient(135deg, ${theme.primary})` }}>
                <motion.div
                  className="absolute inset-0"
                  style={{ background: `linear-gradient(135deg, ${theme.primary})` }}
                  animate={{
                    opacity: [0.8, 1, 0.8],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                />
                <span className="relative z-10 flex items-center gap-1">
                  <Sparkles className="h-3 w-3" />
                  {t(`highlights.${product.highlight}`) || product.highlight}
                </span>
              </div>
            </motion.div>
          )}

          {/* 卡片内容 */}
          <div className="relative p-4 sm:p-6 lg:p-8 flex flex-col h-full">
            {/* 头部区域 */}
            <div className="flex items-start justify-between mb-4 sm:mb-6 lg:mb-8">
              {/* 主图标 */}
              <motion.div
                className="relative"
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.3 }}
              >
                <div className="relative">
                  <motion.div
                    className="w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 rounded-xl sm:rounded-2xl flex items-center justify-center shadow-lg relative overflow-hidden"
                    style={{ background: `linear-gradient(135deg, ${theme.primary})` }}
                    whileHover={{ rotate: [0, -5, 5, 0] }}
                    transition={{ duration: 0.5 }}
                  >
                    {/* 图标光晕 */}
                    <motion.div
                      className="absolute inset-0 rounded-2xl"
                      style={{ background: `linear-gradient(135deg, ${theme.primary})` }}
                      animate={{
                        opacity: [0.8, 1, 0.8],
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                    />
                    {renderIcon(product.iconName, "h-6 w-6 sm:h-7 sm:w-7 lg:h-8 lg:w-8 text-white relative z-10")}
                  </motion.div>

                  {/* 外部光晕 */}
                  <motion.div
                    className="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                    style={{
                      background: `linear-gradient(135deg, ${theme.primary})`,
                      filter: 'blur(16px)',
                      transform: 'scale(1.2)'
                    }}
                  />
                </div>
              </motion.div>

              {/* 右上角操作按钮 */}
              <div className="flex items-center gap-2">
                {/* 分类标签 */}
                {product.category && (
                  <motion.span
                    className="px-3 py-1.5 text-xs font-semibold rounded-full bg-white/60 backdrop-blur-sm border border-white/30 shadow-sm"
                    style={{ color: theme.accent }}
                    whileHover={{ scale: 1.05 }}
                  >
                    {product.category}
                  </motion.span>
                )}

                {/* 收藏按钮 */}
                <motion.button
                  className={cn(
                    "p-2 rounded-full bg-white/60 backdrop-blur-sm border border-white/30 shadow-sm hover:bg-white/80 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
                    isBookmarkLoading && "opacity-50 cursor-not-allowed"
                  )}
                  whileHover={!isBookmarkLoading ? { scale: 1.1 } : {}}
                  whileTap={!isBookmarkLoading ? { scale: 0.95 } : {}}
                  onClick={handleBookmarkClick}
                  aria-label={isBookmarked ? t('card.removeBookmark') : t('card.addBookmark')}
                  aria-pressed={isBookmarked}
                  disabled={isBookmarkLoading}
                >
                  {isBookmarkLoading ? (
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-slate-400 border-t-transparent" />
                  ) : (
                    <Bookmark
                      className={cn(
                        "h-4 w-4 transition-colors",
                        isBookmarked ? "fill-current" : ""
                      )}
                      style={{ color: theme.accent }}
                    />
                  )}
                </motion.button>
              </div>
            </div>

            {/* 标题和描述 */}
            <div className="mb-4 sm:mb-6 lg:mb-8">
              <motion.h3
                className="text-lg sm:text-xl lg:text-2xl font-bold text-slate-800 mb-2 sm:mb-3 lg:mb-4 leading-tight"
                style={{
                  background: `linear-gradient(135deg, #1e293b, ${theme.accent})`,
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent'
                }}
                whileHover={{
                  scale: 1.02,
                  transition: { duration: 0.2 }
                }}
              >
                {product.name}
              </motion.h3>
              <p className="text-slate-600 leading-relaxed line-clamp-3 text-xs sm:text-sm">
                {product.description}
              </p>
            </div>

            {/* 特性展示 */}
            <div className="mb-4 sm:mb-6 lg:mb-8 flex-grow">
              <div className="grid grid-cols-1 gap-3">
                {product.features.slice(0, 3).map((feature, featureIndex) => (
                  <motion.div
                    key={feature.text}
                    className="group/feature relative"
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{
                      duration: 0.4,
                      delay: featureIndex * 0.1 + 0.2,
                      type: "spring",
                      stiffness: 100
                    }}
                    viewport={{ once: true }}
                    whileHover={{ x: 4 }}
                  >
                    <div className="flex items-center gap-2 sm:gap-3 p-2 sm:p-3 rounded-lg sm:rounded-xl bg-gradient-to-r from-white/40 to-white/20 backdrop-blur-sm border border-white/30 hover:from-white/60 hover:to-white/40 transition-all duration-300">
                      {/* 特性图标 */}
                      <motion.div
                        className="w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 rounded-md sm:rounded-lg flex items-center justify-center flex-shrink-0 shadow-sm"
                        style={{ background: `linear-gradient(135deg, ${theme.primary})` }}
                        whileHover={{ scale: 1.1, rotate: 5 }}
                        transition={{ duration: 0.2 }}
                      >
                        {renderIcon(feature.iconName, "h-3 w-3 sm:h-4 sm:w-4 text-white")}
                      </motion.div>

                      {/* 特性文本 */}
                      <span className="text-xs sm:text-sm font-medium text-slate-700 group-hover/feature:text-slate-900 transition-colors flex-1">
                        {feature.text}
                      </span>

                      {/* 箭头指示器 */}
                      <motion.div
                        className="opacity-0 group-hover/feature:opacity-100 transition-opacity"
                        initial={{ x: -10 }}
                        whileHover={{ x: 0 }}
                      >
                        <ChevronRight className="h-3 w-3 sm:h-4 sm:w-4 text-slate-400" />
                      </motion.div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>

            {/* 统计信息卡片 */}
            <motion.div
              className="mb-3 sm:mb-4 lg:mb-6 p-3 sm:p-4 rounded-xl sm:rounded-2xl bg-gradient-to-r from-white/50 to-white/30 backdrop-blur-sm border border-white/40 shadow-sm"
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
            >
              <div className="grid grid-cols-3 gap-2 sm:gap-3 lg:gap-4">
                <div className="text-center">
                  <motion.div
                    className="text-sm sm:text-base lg:text-lg font-bold mb-1"
                    style={{ color: theme.accent }}
                    animate={{ scale: [1, 1.05, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  >
                    99%
                  </motion.div>
                  <div className="text-xs sm:text-xs text-slate-500">{t('card.satisfaction')}</div>
                </div>
                <div className="text-center border-x border-white/30">
                  <div className="text-sm sm:text-base lg:text-lg font-bold mb-1" style={{ color: theme.accent }}>
                    <Clock className="h-3 w-3 sm:h-4 sm:w-4 inline mr-1" />
                    24/7
                  </div>
                  <div className="text-xs sm:text-xs text-slate-500">{t('card.support')}</div>
                </div>
                <div className="text-center">
                  <div className="text-sm sm:text-base lg:text-lg font-bold mb-1 flex items-center justify-center gap-1" style={{ color: theme.accent }}>
                    <Star className="h-3 w-3 sm:h-4 sm:w-4 fill-current" />
                    4.9
                  </div>
                  <div className="text-xs sm:text-xs text-slate-500">{t('card.rating')}</div>
                </div>
              </div>
            </motion.div>

            {/* 价格信息 */}
            {product.price && (
              <motion.div
                className="mb-3 sm:mb-4 lg:mb-6 p-3 sm:p-4 rounded-xl sm:rounded-2xl bg-gradient-to-r from-white/60 to-white/40 backdrop-blur-sm border border-white/40 shadow-sm"
                whileHover={{ scale: 1.02 }}
                transition={{ duration: 0.2 }}
              >
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-xs text-slate-500 mb-1 flex items-center gap-1">
                      <TrendingUp className="h-3 w-3" />
                      {t('card.startingPrice')}
                    </div>
                    <motion.div
                      className="text-lg sm:text-xl font-bold"
                      style={{ color: theme.accent }}
                      whileHover={{ scale: 1.05 }}
                    >
                      {product.price}
                    </motion.div>
                  </div>
                  <motion.div
                    className="p-1.5 sm:p-2 rounded-full bg-green-100"
                    whileHover={{ scale: 1.1, rotate: 5 }}
                  >
                    <TrendingUp className="h-4 w-4 sm:h-5 sm:w-5 text-green-600" />
                  </motion.div>
                </div>
              </motion.div>
            )}

            {/* 底部操作区 */}
            <div className="flex items-center gap-2 sm:gap-3">
              {/* 喜欢按钮 */}
              <motion.button
                className={cn(
                  "flex items-center justify-center p-2 sm:p-3 rounded-lg sm:rounded-xl bg-white/60 backdrop-blur-sm border border-white/40 shadow-sm hover:bg-white/80 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
                  isLikeLoading && "opacity-50 cursor-not-allowed"
                )}
                whileHover={!isLikeLoading ? { scale: 1.05 } : {}}
                whileTap={!isLikeLoading ? { scale: 0.95 } : {}}
                onClick={handleLikeClick}
                aria-label={isLiked ? t('card.removeLike') : t('card.addLike')}
                aria-pressed={isLiked}
                disabled={isLikeLoading}
              >
                {isLikeLoading ? (
                  <div className="h-4 w-4 sm:h-5 sm:w-5 animate-spin rounded-full border-2 border-slate-400 border-t-transparent" />
                ) : (
                  <Heart
                    className={cn(
                      "h-4 w-4 sm:h-5 sm:w-5 transition-colors",
                      isLiked ? "fill-red-500 text-red-500" : "text-slate-400"
                    )}
                  />
                )}
              </motion.button>

              {/* 主要按钮 */}
              <motion.div
                className="flex-1"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Button
                  asChild
                  className="w-full h-10 sm:h-11 lg:h-12 rounded-lg sm:rounded-xl font-semibold text-white shadow-lg border-0 relative overflow-hidden group/btn text-sm sm:text-base"
                  style={{ background: `linear-gradient(135deg, ${theme.primary})` }}
                >
                  <Link href={`/products/${product.slug}`} className="flex items-center justify-center gap-2">
                    {/* 按钮背景动画 */}
                    <motion.div
                      className="absolute inset-0"
                      style={{ background: `linear-gradient(135deg, ${theme.primary})` }}
                      whileHover={{
                        background: `linear-gradient(135deg, ${theme.primary.replace('500', '600').replace('600', '700')})`
                      }}
                    />

                    <span className="relative z-10">{t('card.learnMore')}</span>
                    <motion.div
                      className="relative z-10"
                      animate={{ x: [0, 4, 0] }}
                      transition={{ duration: 1.5, repeat: Infinity }}
                    >
                      <ArrowRight className="h-4 w-4 sm:h-5 sm:w-5" />
                    </motion.div>
                  </Link>
                </Button>
              </motion.div>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  )
}
